import { View, ScrollView } from "@tarojs/components";
import { useState, useEffect } from "react";
import Taro from "@tarojs/taro";
import IconFont from "@/components/iconfont";

// 定义店铺库存数据类型
interface StoreInventoryItem {
  code: number;
  name: string;
  address: string;
  mobile: string;
  store_stock: number;
}

interface StoreInventoryPopupProps {
  visible: boolean;
  onClose: () => void;
  barcode?: string;
}

const StoreInventoryPopup = ({
  visible,
  onClose,
  barcode,
}: StoreInventoryPopupProps) => {
  const [storeList, setStoreList] = useState<StoreInventoryItem[]>([
    {
      code: 1,
      name: "Shanghai Anfu Store",
      address: "No. 500 Dongyu Road, Pudong New District, Shanghai",
      mobile: "021-12345678",
      store_stock: 10,
    },
    {
      code: 1,
      name: "Shanghai Anfu Store",
      address: "No. 500 Dongyu Road, Pudong New District, Shanghai",
      mobile: "021-12345678",
      store_stock: 0,
    },
    {
      code: 1,
      name: "Shanghai Anfu Store",
      address: "No. 500 Dongyu Road, Pudong New District, Shanghai",
      mobile: "021-12345678",
      store_stock: 8,
    },
    {
      code: 1,
      name: "Shanghai Anfu Store",
      address: "No. 500 Dongyu Road, Pudong New District, Shanghai",
      mobile: "021-12345678",
      store_stock: 8,
    },
  ]);

  // 获取店铺库存数据
  const fetchStoreInventory = async () => {
    if (!barcode) return;

    try {
      const res = await Taro.request({
        url: "/web/mobile/default.php?c=goods&m=store_stock",
        method: "GET",
        data: { barcode },
      });

      if (res.data.code === 0) {
        setStoreList(res.data.data);
      }
    } catch (error) {
      console.error("获取店铺库存失败:", error);
    } finally {
    }
  };

  // 打电话
  const makePhoneCall = (phoneNumber: string) => {
    Taro.makePhoneCall({
      phoneNumber: phoneNumber,
    }).catch((error) => {
      console.error("拨打电话失败:", error);
    });
  };

  useEffect(() => {
    // if (visible && barcode) {
    fetchStoreInventory();
    // }
  }, [visible, barcode]);

  if (!visible) return null;

  return (
    <View className="fixed inset-0 z-50">
      {/* 遮罩层 */}
      <View
        className="absolute inset-0 bg-black bg-opacity-50"
        onClick={onClose}
      />

      {/* 弹窗内容 */}
      <View className="absolute bottom-0 left-0 right-0 bg-white py-9 px-8">
        {/* 固定标题区域 */}
        <View className="flex justify-between items-center font-normal">
          <View className="text-md leading-10 text-black">
            Nearby Store Inventory
          </View>
          <View onClick={onClose} className="p-2">
            <IconFont name="close" size={24} color="#000" />
          </View>
        </View>

        {/* 可滚动内容区域 */}
        <ScrollView
          scrollY
          type="list"
          style={{ height: "455px" }} // 519px - 64px(标题区域) = 455px
        >
          <View className="">
            {storeList.length > 0 ? (
              storeList.map((store, index) => (
                <View key={store.code} className="py-12 border-b-1">
                  {/* 店铺名称 */}
                  <View className="text-base font-medium font-gt-america-medium text-brand-2D2E2C mb-4">
                    {store.name}
                  </View>

                  {/* 地址 */}
                  <View className="flex items-start mb-4">
                    <IconFont name="address" size={16} color="#898989" />
                    <View className="text-sm text-brand-898989 flex-1 ml-2">
                      {store.address}
                    </View>
                  </View>

                  {/* 电话 */}
                  <View className="flex items-center mb-10">
                    <IconFont name="call" size={16} color="#898989" />
                    <View
                      className="text-sm text-brand-898989 underline ml-2"
                      onClick={() => makePhoneCall(store.mobile)}
                    >
                      {store.mobile}
                    </View>
                  </View>

                  {/* 库存 */}
                  <View className="text-sm text-brand-2D2E2C flex-start">
                    In-store stock:
                    <View
                      className={[
                        "bg-brand-000000 text-white rounded-50 text-sm inline-block leading-8 h-8 px-2 text-center ml-2",
                        store.store_stock == 0 && "bg-brand-BABABA",
                      ].join(" ")}
                    >
                      {store.store_stock}
                    </View>
                  </View>
                </View>
              ))
            ) : (
              <View className="flex justify-center items-center py-8 text-sm">
                <View className="text-brand-898989">No Store Inventory.</View>
              </View>
            )}
          </View>
        </ScrollView>
      </View>
    </View>
  );
};

export default StoreInventoryPopup;
