import { View, ScrollView } from "@tarojs/components";
import { useState, useEffect } from "react";
import Taro from "@tarojs/taro";
import IconFont from "@/components/iconfont";

// 定义店铺库存数据类型
interface StoreInventoryItem {
  code: number;
  name: string;
  address: string;
  mobile: string;
  store_stock: number;
}

interface StoreInventoryPopupProps {
  visible: boolean;
  onClose: () => void;
  barcode?: string;
}

const StoreInventoryPopup = ({
  visible,
  onClose,
  barcode,
}: StoreInventoryPopupProps) => {
  const [storeList, setStoreList] = useState<StoreInventoryItem[]>([]);
  const [loading, setLoading] = useState(false);

  // 获取店铺库存数据
  const fetchStoreInventory = async () => {
    if (!barcode) return;

    try {
      setLoading(true);
      const res = await Taro.request({
        url: "/web/mobile/default.php?c=goods&m=store_stock",
        method: "GET",
        data: { barcode },
      });

      if (res.data.code === 0) {
        setStoreList(res.data.data);
      }
    } catch (error) {
      console.error("获取店铺库存失败:", error);
      // 使用mock数据作为fallback
      setStoreList([
        {
          code: 1,
          name: "SUITSUPPLY上海店",
          address: "上海市黄浦区南京东路123号",
          mobile: "021-12345678",
          store_stock: 10,
        },
        {
          code: 2,
          name: "SUITSUPPLY北京店",
          address: "北京市朝阳区建国门外大街1号",
          mobile: "010-87654321",
          store_stock: 5,
        },
        {
          code: 3,
          name: "SUITSUPPLY广州店",
          address: "广州市天河区天河路208号",
          mobile: "020-11223344",
          store_stock: 8,
        },
      ]);
    } finally {
      setLoading(false);
    }
  };

  // 打电话
  const makePhoneCall = (phoneNumber: string) => {
    Taro.makePhoneCall({
      phoneNumber: phoneNumber,
    }).catch((error) => {
      console.error("拨打电话失败:", error);
    });
  };

  useEffect(() => {
    if (visible && barcode) {
      fetchStoreInventory();
    }
  }, [visible, barcode]);

  if (!visible) return null;

  return (
    <View className="fixed inset-0 z-50">
      {/* 遮罩层 */}
      <View
        className="absolute inset-0 bg-black bg-opacity-50"
        onClick={onClose}
      />

      {/* 弹窗内容 */}
      <View className="absolute bottom-0 left-0 right-0 bg-white rounded-t-lg">
        {/* 固定标题区域 */}
        <View className="flex justify-between items-center px-4 py-4 border-b border-gray-200">
          <View className="text-md leading-5 text-brand-2D2E2C">
            Nearby Store Inventory
          </View>
          <View onClick={onClose} className="p-2">
            <IconFont name="close" size={20} color="#2D2E2C" />
          </View>
        </View>

        {/* 可滚动内容区域 */}
        <ScrollView
          scrollY
          type="list"
          className="px-4"
          style={{ height: "455px" }} // 519px - 64px(标题区域) = 455px
        >
          <View className="py-6">
            {loading ? (
              <View className="flex justify-center items-center py-8">
                <View className="text-brand-898989">加载中...</View>
              </View>
            ) : storeList.length > 0 ? (
              storeList.map((store, index) => (
                <View key={store.code} className={`${index > 0 ? "mt-6" : ""}`}>
                  {/* 店铺名称 */}
                  <View className="text-base font-medium text-brand-2D2E2C mb-3">
                    {store.name}
                  </View>

                  {/* 地址 */}
                  <View className="flex items-start mb-2">
                    <IconFont
                      name="address"
                      size={16}
                      color="#898989"
                      className="mt-0.5 mr-2"
                    />
                    <View className="text-sm text-brand-898989 flex-1">
                      {store.address}
                    </View>
                  </View>

                  {/* 电话 */}
                  <View className="flex items-center mb-2">
                    <IconFont
                      name="call"
                      size={16}
                      color="#898989"
                      className="mr-2"
                    />
                    <View
                      className="text-sm text-brand-2D2E2C underline"
                      onClick={() => makePhoneCall(store.mobile)}
                    >
                      {store.mobile}
                    </View>
                  </View>

                  {/* 库存 */}
                  <View className="text-sm text-brand-2D2E2C">
                    In-store stock: {store.store_stock}
                  </View>

                  {/* 分割线 */}
                  {index < storeList.length - 1 && (
                    <View className="border-b border-gray-100 mt-6" />
                  )}
                </View>
              ))
            ) : (
              <View className="flex justify-center items-center py-8">
                <View className="text-brand-898989">暂无店铺库存信息</View>
              </View>
            )}
          </View>
        </ScrollView>
      </View>
    </View>
  );
};

export default StoreInventoryPopup;
