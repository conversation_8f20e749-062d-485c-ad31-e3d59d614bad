import { <PERSON>, But<PERSON> } from "@tarojs/components";
import { observer } from "mobx-react";
import { useState } from "react";

import { MainLayout } from "@/components";
import { useSafeAreaBottom } from "@/hook";

const SizePage = () => {
  const safeAreaBottom = useSafeAreaBottom();

  // 模拟尺码数据 - 包含库存信息 (s: 渠道S库存, n: 渠道N库存)
  const sizeItems = [
    { id: 1, size: "XS", stock: { s: 5, n: 3 } },
    { id: 2, size: "S", stock: { s: 0, n: 2 } },
    { id: 3, size: "M", stock: { s: 8, n: 5 } },
    { id: 4, size: "L", stock: { s: 0, n: 0 } }, // 无库存
    { id: 5, size: "XL", stock: { s: 3, n: 0 } },
    { id: 6, size: "XXL", stock: { s: 0, n: 0 } }, // 无库存
    { id: 7, size: "38", stock: { s: 2, n: 4 } },
    { id: 8, size: "40", stock: { s: 0, n: 0 } }, // 无库存
    { id: 9, size: "42", stock: { s: 6, n: 2 } },
    { id: 10, size: "44", stock: { s: 1, n: 3 } },
    { id: 11, size: "46", stock: { s: 0, n: 1 } },
    { id: 12, size: "48", stock: { s: 4, n: 0 } },
  ];

  // 测试单个item的情况，取消注释下面这行
  // const sizeItems = [{ id: 1, size: "M", stock: { s: 5, n: 3 } }];

  // 判断是否有库存
  const hasStock = (item: typeof sizeItems[0]) => item.stock.s > 0 || item.stock.n > 0;

  const [selectedSize, setSelectedSize] = useState<number | null>(null);

  return (
    <MainLayout
      initOptions={{
        inited: true,
        initLoading: true,
        loadingFullScreen: true,
      }}
      className="size_page"
      headerType="withBack"
      showBottomPlaceholder={false}
    >
      <View className="flex flex-col h-full" style={{ backgroundColor: '#F6F6F6' }}>
        {/* Title */}
        <View className="px-4 py-6">
          <View style={{ fontSize: '16px', lineHeight: '20px', color: '#2D2E2C' }}>Select size</View>
        </View>

        {/* Size Grid Container */}
        <View className="flex-1 px-4 flex justify-center">
          {sizeItems.length === 1 ? (
            /* Single item centered at bottom */
            <View className="flex justify-center items-end h-full pb-32">
              <View
                className={`relative rounded-lg flex-center ${
                  !hasStock(sizeItems[0])
                    ? 'cursor-not-allowed'
                    : selectedSize === sizeItems[0].id
                    ? 'bg-black'
                    : 'bg-white'
                }`}
                style={{
                  width: '104px',
                  height: '40px',
                  border: !hasStock(sizeItems[0])
                    ? '1px solid #E1E1E1'
                    : selectedSize === sizeItems[0].id
                    ? '1px solid #000'
                    : '1px solid #E1E1E1',
                  backgroundColor: !hasStock(sizeItems[0])
                    ? '#F6F6F6'
                    : selectedSize === sizeItems[0].id
                    ? '#000'
                    : '#fff',
                }}
                onClick={() => hasStock(sizeItems[0]) && setSelectedSize(sizeItems[0].id)}
              >
                {/* Size Text */}
                <View style={{
                  color: !hasStock(sizeItems[0])
                    ? '#898989'
                    : selectedSize === sizeItems[0].id
                    ? '#fff'
                    : '#2D2E2C'
                }}>{sizeItems[0].size}</View>

                {/* S and N letters in top right */}
                <View className="absolute flex" style={{ top: '8px', right: '8px' }}>
                  <View style={{
                    fontSize: '10px',
                    color: !hasStock(sizeItems[0])
                      ? '#898989'
                      : selectedSize === sizeItems[0].id
                      ? '#fff'
                      : '#2D2E2C',
                    marginRight: '2px'
                  }}>S</View>
                  <View style={{
                    fontSize: '10px',
                    color: !hasStock(sizeItems[0])
                      ? '#898989'
                      : selectedSize === sizeItems[0].id
                      ? '#fff'
                      : '#2D2E2C'
                  }}>N</View>
                </View>
              </View>
            </View>
          ) : (
            /* Multiple items in grid */
            <View
              style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(3, 104px)',
                gap: '8px',
                justifyContent: 'center',
                gridAutoFlow: 'column',
                gridTemplateRows: `repeat(${Math.ceil(sizeItems.length / 3)}, 40px)`,
              }}
            >
              {sizeItems.map((item) => (
                <View
                  key={item.id}
                  className={`relative rounded-lg flex-center ${
                    !hasStock(item) ? 'cursor-not-allowed' : ''
                  }`}
                  style={{
                    width: '104px',
                    height: '40px',
                    border: !hasStock(item)
                      ? '1px solid #E1E1E1'
                      : selectedSize === item.id
                      ? '1px solid #000'
                      : '1px solid #E1E1E1',
                    backgroundColor: !hasStock(item)
                      ? '#F6F6F6'
                      : selectedSize === item.id
                      ? '#000'
                      : '#fff',
                  }}
                  onClick={() => hasStock(item) && setSelectedSize(item.id)}
                >
                  {/* Size Text */}
                  <View style={{
                    color: !hasStock(item)
                      ? '#898989'
                      : selectedSize === item.id
                      ? '#fff'
                      : '#2D2E2C'
                  }}>{item.size}</View>

                  {/* S and N letters in top right */}
                  <View className="absolute flex" style={{ top: '8px', right: '8px' }}>
                    <View style={{
                      fontSize: '10px',
                      color: !hasStock(item)
                        ? '#898989'
                        : selectedSize === item.id
                        ? '#fff'
                        : '#2D2E2C',
                      marginRight: '2px'
                    }}>S</View>
                    <View style={{
                      fontSize: '10px',
                      color: !hasStock(item)
                        ? '#898989'
                        : selectedSize === item.id
                        ? '#fff'
                        : '#2D2E2C'
                    }}>N</View>
                  </View>
                </View>
              ))}
            </View>
          )}
        </View>

        {/* Fixed Bottom Buttons */}
        <View
          className="fixed bottom-0 left-0 right-0 border-t-1 px-4 py-4 flex"
          style={{
            backgroundColor: '#F6F6F6',
            paddingBottom: `${safeAreaBottom.safeAreaBottom + 16}px`
          }}
        >
          <View className="flex-1 mr-3">
            <Button className="btn-white w-full">
              Cancel
            </Button>
          </View>
          <View className="flex-1">
            <Button className="btn-black w-full">
              Confirm
            </Button>
          </View>
        </View>
      </View>
    </MainLayout>
  );
};

export default observer(SizePage);
