import { View, Button, Image } from "@tarojs/components";
import { observer } from "mobx-react";
import { useState, useEffect } from "react";
import Taro from "@tarojs/taro";

import { MainLayout } from "@/components";
import { useSafeAreaBottom } from "@/hook";
import http from "@/http";
import cm from "@/assets/icon/custom-made.svg";
import StoreInventoryPopup from "./StoreInventoryPopup";

// 定义尺码数据类型
interface SizeItem {
  size: string;
  barcode: string;
  store_stock: number; // S渠道库存
  network_stock: number; // N渠道库存
  cm_url: string;
}

const SizePage = () => {
  const safeAreaBottom = useSafeAreaBottom();
  const [sizeItems, setSizeItems] = useState<SizeItem[]>([]);
  const [selectedItem, setSelectedItem] = useState<SizeItem | null>(null);
  const [screenInfo, setScreenInfo] = useState({
    windowHeight: 0,
    windowWidth: 0,
  });
  const [showStorePopup, setShowStorePopup] = useState(true);

  // 判断是否有库存
  const hasStock = (item: SizeItem) =>
    item.store_stock > 0 || item.network_stock > 0;

  // 计算最大高度 - 基于每行50px
  const calculateMaxHeight = () => {
    if (screenInfo.windowHeight === 0) return 400; // 默认高度

    const rowHeight = 50; // 每行高度50px (40px item + 10px margin)
    const headerHeight = 100; // 标题区域高度
    const buttonHeight = 120; // 底部按钮区域高度
    const availableHeight =
      screenInfo.windowHeight -
      headerHeight -
      buttonHeight -
      safeAreaBottom.safeAreaBottom;

    const maxRows = Math.floor(availableHeight / rowHeight);
    const actualMaxRows = Math.min(maxRows, 9); // 最多9行

    const calculatedHeight = actualMaxRows * rowHeight;

    return calculatedHeight;
  };

  // 获取item样式类名
  const getItemClassName = (item: SizeItem) => {
    const baseClass = "relative rounded-lg flex-center border";
    if (!hasStock(item)) {
      return `${baseClass} cursor-not-allowed bg-brand-F6F6F6 border-1-area`;
    }
    if (selectedItem?.barcode === item.barcode) {
      return `${baseClass} bg-brand-000000 border-brand-000000`;
    }
    return `${baseClass} bg-white border-brand-E1E1E1`;
  };

  // 获取文字样式类名
  const getTextClassName = (item: SizeItem) => {
    if (!hasStock(item)) return "text-brand-898989";
    if (selectedItem?.barcode === item.barcode) return "text-white";
    return "text-brand-2D2E2C";
  };

  // 可复用的尺码项组件
  const SizeItemComponent = ({ item }: { item: SizeItem }) => (
    <View
      className={getItemClassName(item)}
      style={{
        width: "104px",
        height: "40px",
        marginLeft: "4px",
        marginRight: "4px",
      }}
      onClick={() => {
        if (hasStock(item)) {
          // 如果点击的是已选中的item，则取消选中；否则选中该item
          setSelectedItem(selectedItem?.barcode === item.barcode ? null : item);
        }
      }}
    >
      {/* Size Text */}
      <View className={getTextClassName(item)}>{item.size}</View>

      {/* S and N letters in top right - 只在有库存时显示 */}
      {hasStock(item) && (
        <View className="absolute flex" style={{ top: "8px", right: "8px" }}>
          <View className={`text-xs mr-1 ${getTextClassName(item)}`}>S</View>
          <View className={`text-xs ${getTextClassName(item)}`}>N</View>
        </View>
      )}
    </View>
  );

  // 获取尺码数据
  const fetchSizeData = async () => {
    try {
      const res: any = await http.order.getGoodsSizeList({
        goods_sn: "test_goods_sn", // 这里应该从路由参数获取
        color: "test_color", // 这里应该从路由参数获取
      });

      if (res.data.code === 0) {
        setSizeItems(res.data.data);
      }
    } catch (error) {
      console.error("获取尺码数据失败:", error);
      // 使用mock数据作为fallback
      setSizeItems([
        {
          size: "S",
          barcode: "1234567890001",
          store_stock: 0,
          network_stock: 15,
          cm_url: "",
        },
        {
          size: "M",
          barcode: "1234567890002",
          store_stock: 8,
          network_stock: 20,
          cm_url: "",
        },
        {
          size: "L",
          barcode: "1234567890003",
          store_stock: 0,
          network_stock: 0,
          cm_url: "",
        },
        {
          size: "XL",
          barcode: "1234567890004",
          store_stock: 2,
          network_stock: 8,
          cm_url: "",
        },
      ]);

      // 测试单个item的情况，取消注释下面这行
      // setSizeItems([{ size: 'M', barcode: '1234567890002', store_stock: 8, network_stock: 20, cm_url: '' }]);
    } finally {
    }
  };

  // 获取屏幕信息
  const getScreenInfo = async () => {
    try {
      const systemInfo = await Taro.getSystemInfo();
      setScreenInfo({
        windowHeight: systemInfo.windowHeight,
        windowWidth: systemInfo.windowWidth,
      });
    } catch (error) {
      console.error("获取屏幕信息失败:", error);
      // 设置默认值
      setScreenInfo({
        windowHeight: 667, // iPhone 6/7/8 默认高度
        windowWidth: 375,
      });
    }
  };

  useEffect(() => {
    getScreenInfo();
    fetchSizeData();
  }, []);

  return (
    <MainLayout
      initOptions={{
        inited: true,
        initLoading: true,
        loadingFullScreen: true,
      }}
      className="size_page"
      headerType="withBack"
      showBottomPlaceholder={true}
      headerBackgroundColor="#F6F6F6"
      bgColor="#F6F6F6"
    >
      <View className="flex flex-col h-full bg-brand-F6F6F6 relative">
        {/* Title */}
        <View className="absolute bottom-80 left-0 right-0">
          <View className="px-4 py-6 mb-16">
            <View className="text-md leading-10 text-brand-2D2E2C mt-auto text-center">
              Select size
            </View>
          </View>

          <View
            className="flex-1 px-4 flex justify-center"
            style={{
              maxHeight: `${calculateMaxHeight()}px`,
              overflow: "hidden",
            }}
          >
            {sizeItems.length === 1 ? (
              <View className="flex justify-center items-end h-full pb-32">
                <SizeItemComponent item={sizeItems[0]} />
              </View>
            ) : (
              /* Multiple items in flex layout - 从下往上，从左往右排列 */
              <View
                className="flex flex-col-reverse justify-center mt-auto"
                style={{
                  maxHeight: `${calculateMaxHeight()}px`,
                  overflow: "hidden",
                }}
              >
                {/* 将items按行分组，每行3个 */}
                {Array.from(
                  { length: Math.ceil(sizeItems.length / 3) },
                  (_, rowIndex) => {
                    const startIndex = rowIndex * 3;
                    const rowItems = sizeItems.slice(
                      startIndex,
                      startIndex + 3
                    );
                    return (
                      <View
                        key={rowIndex}
                        className="flex justify-center"
                        style={{
                          marginBottom: "10px",
                          height: "40px", // 确保每个item高度一致
                        }}
                      >
                        {rowItems.map((item) => (
                          <SizeItemComponent key={item.barcode} item={item} />
                        ))}
                      </View>
                    );
                  }
                )}
              </View>
            )}
          </View>
        </View>
        {/* Fixed Bottom */}
        <View className="absolute bottom-0 left-0 right-0 px-4 bg-white ">
          {selectedItem && (
            <View className="flex-between h-8  mx-8 my-6 font-light text-sm flex break-keep">
              <View className="leading-8  flex">
                <View className="flex-center">
                  In-store stock:
                  <View
                    className={[
                      "bg-brand-000000 text-white rounded-50 text-sm inline-block leading-8 h-8 px-2 text-center ml-2",
                      selectedItem.store_stock == 0 && "bg-brand-BABABA",
                    ].join(" ")}
                  >
                    {selectedItem.store_stock}
                  </View>
                </View>
                <View className="line text-brand-E1E1E1 text-xs w-1 mx-2">
                  |
                </View>
                <View className="flex-center">
                  Network:
                  <span
                    className={`
                        bg-brand-000000 text-white rounded-50 text-sm inline-block leading-8 h-8 px-2 text-center ml-2
                        ${selectedItem.network_stock == 0 && " bg-brand-BABABA"}
                      `}
                  >
                    {selectedItem.network_stock}
                  </span>
                </View>
              </View>

              <View
                className="underline font-light text-sm leading-8 text-right cursor-pointer"
                onClick={() => setShowStorePopup(true)}
              >
                Nearby Store Inventory
              </View>
            </View>
          )}
          <View className="flex mx-8 my-6">
            <Button className="btn-white w-full mr-3 flex-center">
              <Image src={cm} className="w-8 h-8 mr-2"></Image>
              Custom Made
            </Button>
            <Button className="btn-black w-full ml-3">
              Scan to Add to bag
            </Button>
          </View>
        </View>

        {/* Store Inventory Popup */}
        <StoreInventoryPopup
          visible={showStorePopup}
          onClose={() => setShowStorePopup(false)}
          barcode={selectedItem?.barcode}
        />
      </View>
    </MainLayout>
  );
};

export default observer(SizePage);
