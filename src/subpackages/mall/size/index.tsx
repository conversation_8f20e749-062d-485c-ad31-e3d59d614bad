import { View, Button } from "@tarojs/components";
import { observer } from "mobx-react";
import { useState, useEffect } from "react";
import Taro from "@tarojs/taro";

import { MainLayout } from "@/components";
import { useSafeAreaBottom } from "@/hook";

// 定义尺码数据类型
interface SizeItem {
  size: string;
  barcode: string;
  store_stock: number; // S渠道库存
  network_stock: number; // N渠道库存
  cm_url: string;
}

const SizePage = () => {
  const safeAreaBottom = useSafeAreaBottom();
  const [sizeItems, setSizeItems] = useState<SizeItem[]>([]);
  const [selectedSize, setSelectedSize] = useState<string | null>("");
  const [loading, setLoading] = useState(true);

  // 判断是否有库存
  const hasStock = (item: SizeItem) =>
    item.store_stock > 0 || item.network_stock > 0;

  // 获取item样式类名
  const getItemClassName = (item: SizeItem) => {
    const baseClass = "relative rounded-lg flex-center border";
    if (!hasStock(item)) {
      return `${baseClass} cursor-not-allowed bg-brand-F6F6F6 border-1-area`;
    }
    if (selectedSize === item.size) {
      return `${baseClass} bg-brand-000000 border-brand-000000`;
    }
    return `${baseClass} bg-white border-brand-E1E1E1`;
  };

  // 获取文字样式类名
  const getTextClassName = (item: SizeItem) => {
    if (!hasStock(item)) return "text-brand-898989";
    if (selectedSize === item.size) return "text-white";
    return "text-brand-2D2E2C";
  };

  // 可复用的尺码项组件
  const SizeItemComponent = ({ item }: { item: SizeItem }) => (
    <View
      className={getItemClassName(item)}
      style={{
        width: "104px",
        height: "40px",
        marginLeft: "4px",
        marginRight: "4px",
      }}
      onClick={() => hasStock(item) && setSelectedSize(item.size)}
    >
      {/* Size Text */}
      <View className={getTextClassName(item)}>{item.size}</View>

      {/* S and N letters in top right - 只在有库存时显示 */}
      {hasStock(item) && (
        <View className="absolute flex" style={{ top: "8px", right: "8px" }}>
          <View className={`text-xs mr-1 ${getTextClassName(item)}`}>S</View>
          <View className={`text-xs ${getTextClassName(item)}`}>N</View>
        </View>
      )}
    </View>
  );

  // 获取尺码数据
  const fetchSizeData = async () => {
    try {
      setLoading(true);
      const res = await Taro.request({
        url: "/web/mobile/default.php?c=goods&m=size_list",
        method: "GET",
        data: {
          goods_sn: "test_goods_sn", // 这里应该从路由参数获取
          color: "test_color", // 这里应该从路由参数获取
        },
      });

      if (res.data.code === 0) {
        setSizeItems(res.data.data);
      }
    } catch (error) {
      console.error("获取尺码数据失败:", error);
      // 使用mock数据作为fallback
      setSizeItems([
        {
          size: "S",
          barcode: "1234567890001",
          store_stock: 5,
          network_stock: 15,
          cm_url: "",
        },
        {
          size: "M",
          barcode: "1234567890002",
          store_stock: 8,
          network_stock: 20,
          cm_url: "",
        },
        {
          size: "L",
          barcode: "1234567890003",
          store_stock: 0,
          network_stock: 0,
          cm_url: "",
        },
        {
          size: "XL",
          barcode: "1234567890004",
          store_stock: 2,
          network_stock: 8,
          cm_url: "",
        },
      ]);

      // 测试单个item的情况，取消注释下面这行
      // setSizeItems([{ size: 'M', barcode: '1234567890002', store_stock: 8, network_stock: 20, cm_url: '' }]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSizeData();
  }, []);

  return (
    <MainLayout
      initOptions={{
        inited: true,
        initLoading: true,
        loadingFullScreen: true,
      }}
      className="size_page"
      headerType="withBack"
      showBottomPlaceholder={true}
    >
      <View className="flex flex-col h-full bg-brand-F6F6F6">
        {/* Title */}
        <View className="px-4 py-6">
          <View className="text-md leading-10 text-brand-2D2E2C mt-auto text-center">
            Select size
          </View>
        </View>

        {/* Size Grid Container */}
        <View
          className="flex-1 px-4 flex justify-center"
          style={{
            maxHeight: "424px",
            overflow: "hidden",
          }}
        >
          {sizeItems.length === 1 ? (
            /* Single item centered at bottom */
            <View className="flex justify-center items-end h-full pb-32">
              <SizeItemComponent item={sizeItems[0]} />
            </View>
          ) : (
            /* Multiple items in flex layout - 从下往上，从左往右排列 */
            <View
              className="flex flex-col-reverse justify-center"
              style={{
                maxHeight: "424px",
                overflow: "hidden",
              }}
            >
              {/* 将items按行分组，每行3个 */}
              {Array.from(
                { length: Math.ceil(sizeItems.length / 3) },
                (_, rowIndex) => {
                  const startIndex = rowIndex * 3;
                  const rowItems = sizeItems.slice(startIndex, startIndex + 3);
                  return (
                    <View
                      key={rowIndex}
                      className="flex justify-center"
                      style={{ marginBottom: "8px" }}
                    >
                      {rowItems.map((item) => (
                        <SizeItemComponent key={item.barcode} item={item} />
                      ))}
                    </View>
                  );
                }
              )}
            </View>
          )}
        </View>

        {/* Fixed Bottom Buttons */}
        <View
          className="fixed bottom-0 left-0 right-0 border-t-1 px-4 py-4 bg-brand-F6F6F6"
          style={{ paddingBottom: `${safeAreaBottom.safeAreaBottom + 16}px` }}
        >
          { && (
            <View className="flex-between">
              <View>
                {sizeItems[selectedSize]?.store_stock && (
                  <View>
                    In-store stock:{" "}
                    <span className="bg-brand-000000 px-2 rounded">10</span>
                  </View>
                )}
                {sizeItems[selectedSize]?.store_stock &&
                  sizeItems[selectedSize]?.network && (
                    <View className="line"></View>
                  )}
                {sizeItems[selectedSize]?.network && (
                  <View>
                    Network:
                    <span className="bg-brand-000000 px-2 rounded">10</span>
                  </View>
                )}
              </View>
              <View>Nearby Store Inventory</View>
            </View>
          )}

          <View className="flex">
            <Button className="btn-white w-full mr-3">Custom Made</Button>
            <Button className="btn-black w-full ml-3">
              Scan to Add to bag
            </Button>
          </View>
        </View>
      </View>
    </MainLayout>
  );
};

export default observer(SizePage);
