import { View, Button } from "@tarojs/components";
import { observer } from "mobx-react";
import { useState } from "react";

import { MainLayout } from "@/components";
import { useSafeAreaBottom } from "@/hook";

const SizePage = () => {
  const safeAreaBottom = useSafeAreaBottom();

  // 模拟尺码数据 - 可以切换测试单个item和多个item
  const sizeItems = [
    { id: 1, size: "XS" },
    { id: 2, size: "S" },
    { id: 3, size: "M" },
    { id: 4, size: "L" },
    { id: 5, size: "XL" },
    { id: 6, size: "XXL" },
    { id: 7, size: "38" },
    { id: 8, size: "40" },
    { id: 9, size: "42" },
    { id: 10, size: "44" },
    { id: 11, size: "46" },
    { id: 12, size: "48" },
  ];

  // 测试单个item的情况，取消注释下面这行
  // const sizeItems = [{ id: 1, size: "M" }];

  const [selectedSize, setSelectedSize] = useState<number | null>(null);

  return (
    <MainLayout
      initOptions={{
        inited: true,
        initLoading: true,
        loadingFullScreen: true,
      }}
      className="size_page"
      headerType="withBack"
      showBottomPlaceholder={false}
    >
      <View className="flex flex-col h-full bg-white">
        {/* Title */}
        <View className="px-4 py-6">
          <View className="text-xl font-semibold text-gray-900">Select size</View>
        </View>

        {/* Size Grid Container */}
        <View className="flex-1 px-4">
          {sizeItems.length === 1 ? (
            /* Single item centered at bottom */
            <View className="flex justify-center items-end h-full pb-32">
              <View
                className={`relative border-2 rounded-lg flex-center ${
                  selectedSize === sizeItems[0].id
                    ? 'border-black bg-black text-white'
                    : 'border-gray-300 bg-white text-black'
                }`}
                style={{ width: '104px', height: '104px' }}
                onClick={() => setSelectedSize(sizeItems[0].id)}
              >
                {/* Size Text */}
                <View className="text-lg font-medium">{sizeItems[0].size}</View>

                {/* S and N letters in top right */}
                <View className="absolute flex" style={{ top: '8px', right: '8px' }}>
                  <View className="text-xs font-medium opacity-60 mr-1">S</View>
                  <View className="text-xs font-medium opacity-60">N</View>
                </View>
              </View>
            </View>
          ) : (
            /* Multiple items in grid */
            <View
              className="grid"
              style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(3, 104px)',
                gap: '8px',
                justifyContent: 'start',
                gridAutoFlow: 'column',
                gridTemplateRows: `repeat(${Math.ceil(sizeItems.length / 3)}, 104px)`,
              }}
            >
              {sizeItems.map((item) => (
                <View
                  key={item.id}
                  className={`relative border-2 rounded-lg flex-center ${
                    selectedSize === item.id
                      ? 'border-black bg-black text-white'
                      : 'border-gray-300 bg-white text-black'
                  }`}
                  style={{ width: '104px', height: '104px' }}
                  onClick={() => setSelectedSize(item.id)}
                >
                  {/* Size Text */}
                  <View className="text-lg font-medium">{item.size}</View>

                  {/* S and N letters in top right */}
                  <View className="absolute flex" style={{ top: '8px', right: '8px' }}>
                    <View className="text-xs font-medium opacity-60 mr-1">S</View>
                    <View className="text-xs font-medium opacity-60">N</View>
                  </View>
                </View>
              ))}
            </View>
          )}
        </View>

        {/* Fixed Bottom Buttons */}
        <View
          className="fixed bottom-0 left-0 right-0 bg-white border-t-1 px-4 py-4 flex"
          style={{ paddingBottom: `${safeAreaBottom.safeAreaBottom + 16}px` }}
        >
          <View className="flex-1 mr-3">
            <Button className="btn-white w-full">
              Cancel
            </Button>
          </View>
          <View className="flex-1">
            <Button className="btn-black w-full">
              Confirm
            </Button>
          </View>
        </View>
      </View>
    </MainLayout>
  );
};

export default observer(SizePage);
