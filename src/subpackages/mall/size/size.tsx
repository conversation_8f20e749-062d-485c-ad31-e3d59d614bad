import { View, ScrollView } from "@tarojs/components";
import { observer } from "mobx-react";
import { useState } from "react";

import sty from "./index.module.scss";
import { MainLayout, SearchInput, ErrorMessage } from "@/components";
import IconFont from "@/components/iconfont";
import { useStores, useHeaderBarHeight, useSafeAreaBottom } from "@/hook";
import { OrderGoods } from "@/mobx/model/Order";

interface CategoryItem {
  id: string;
  name: string;
  icon?: string;
}

const CatalogPage = () => {
  const { loadingStore } = useStores();
  const [searchValue, setSearchValue] = useState("");
  const [goodsList, setGoodsList] = useState<OrderGoods[]>([]);
  const [searchError, setSearchError] = useState("");
  const { headerBarHeight } = useHeaderBarHeight();
  const { safeAreaBottom } = useSafeAreaBottom();

  return (
   <MainLayout
      className="cash_page"
      showTopPlaceholder={false}
      style={{
        background: `url('https://bq-pim.oss-cn-hangzhou.aliyuncs.com/cms/sit/assets/image/b24d91c0ce56faa998cb04216f31070f.jpeg?x-oss-process=image/format,webp') no-repeat top center`,
        backgroundSize: "100% auto",
        backgroundColor: "#F9F9F9",
      }}
      // 使用便捷配置方式
      headerType="withBack"
      headerTitle="收银台"
      headerColor="#fff"
      headerBackgroundColor="transparent"></MainLayout>
  );
  );
};

export default observer(CatalogPage);
