import { View, ScrollView } from "@tarojs/components";
import { ReactNode } from "react";
import IconFont from "@/components/iconfont";
import SafeAreaBottom from "@/components/SafeAreaBottom";

interface PopupProps {
  visible: boolean;
  onClose: () => void;
  title?: string;
  children?: ReactNode;
  footer?: ReactNode;
  height?: number;
}

const Popup = ({ 
  visible, 
  onClose, 
  title, 
  children, 
  footer,
  height = 530 
}: PopupProps) => {
  if (!visible) return null;

  // 计算内容区域高度
  const titleHeight = 64; // 标题区域高度
  const footerHeight = footer ? 80 : 0; // 底部按钮区域高度（如果有的话）
  const contentHeight = height - titleHeight - footerHeight;

  return (
    <View className="fixed inset-0 z-50">
      {/* 遮罩层 */}
      <View 
        className="absolute inset-0 bg-black bg-opacity-50"
        onClick={onClose}
      />
      
      {/* 弹窗内容 */}
      <View 
        className="absolute bottom-0 left-0 right-0 bg-white rounded-t-lg"
        style={{ height: `${height}px` }}
      >
        {/* 固定标题区域 */}
        {title && (
          <View className="flex justify-between items-center px-4 py-4 border-b border-gray-200">
            <View className="text-md leading-5 text-brand-2D2E2C">
              {title}
            </View>
            <View onClick={onClose} className="p-2">
              <IconFont name="close" size={20} color="#2D2E2C" />
            </View>
          </View>
        )}

        {/* 可滚动内容区域 */}
        <View className="flex-1 relative">
          <ScrollView 
            scrollY 
            type="list"
            className="absolute inset-0"
            style={{ height: `${contentHeight}px` }}
          >
            <View className="px-4 py-6">
              {children}
            </View>
          </ScrollView>
        </View>

        {/* 固定底部按钮区域 */}
        {footer && (
          <View className="border-t border-gray-200">
            <View className="px-4 py-4">
              {footer}
            </View>
            <SafeAreaBottom />
          </View>
        )}

        {/* 如果没有footer，也要处理安全区域 */}
        {!footer && <SafeAreaBottom />}
      </View>
    </View>
  );
};

export default Popup;

// 使用示例：
/*
// 基础用法 - 只有内容
<Popup
  visible={visible}
  onClose={() => setVisible(false)}
  title="标题"
>
  <View>这里是内容</View>
</Popup>

// 带底部按钮的用法
<Popup
  visible={visible}
  onClose={() => setVisible(false)}
  title="标题"
  footer={
    <View className="flex space-x-3">
      <Button className="flex-1 btn-white">取消</Button>
      <Button className="flex-1 btn-black">确认</Button>
    </View>
  }
>
  <View>这里是内容</View>
</Popup>

// 自定义高度
<Popup
  visible={visible}
  onClose={() => setVisible(false)}
  title="标题"
  height={400}
>
  <View>这里是内容</View>
</Popup>

// 无标题的用法
<Popup
  visible={visible}
  onClose={() => setVisible(false)}
>
  <View>这里是内容</View>
</Popup>
*/
